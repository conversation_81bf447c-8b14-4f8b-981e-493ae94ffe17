# CANFAR v1.0 Executive Summary (Diff: v0.7.0 → HEAD)

- Commits: 290
- Files changed / insertions / deletions: 281 / 27,165 / 2,629
- Conventional breakdown (approx.): feat:38, fix:82, docs:19, refactor:23, test:17, build:11, other:99

## Highlights

- Branding and package rename from `skaha` to `canfar` across code, docs, and URLs.
- New, unified `canfar` CLI with expanded commands and clearer UX.
- Modernized auth with OIDC auto-refresh and improved X.509 handling.
- Flattened, typed configuration and new domain models for sessions, registry, and HTTP.
- Extensive documentation overhaul for client and platform usage.
- Tooling/quality upgrades: Python 3.10+, ruff + mypy + richer pytest config, refined CI.

## Breaking Changes

- Package rename:
  - Imports: `from skaha...` → `from canfar...`
  - Modules under `skaha/` removed; equivalent modules live under `canfar/`.
- CLI entrypoints:
  - `skaha` and `cf` entrypoints deprecated; use `canfar`.
  - Removed aliases that conflicted with OS commands (e.g., `canfar list`, `canfar rm`).
- Python version:
  - Requires Python `>= 3.10` (previously 3.9).
- Configuration and models:
  - Flattened config structure; authentication model reworked toward contexts and typed models.
  - Some status and option names adjusted (e.g., `create` uses `--cpu` not `--cpus`).
- Dependency surface:
  - Removed reliance on `pyjwt`; custom token expiry utilities are in `canfar/utils/jwt.py`.
  - Packaging and URLs updated to opencadc/canfar.

## New Features

- CLI (package `canfar.cli`):
  - Core: `canfar` entrypoint via `canfar.cli.main:main`.
  - Auth management: `auth` (list, switch, rm, purge).
  - Session operations: `create`, `delete`, `open` (open sessions in a browser), `ps`, `logs`, `events`, `prune`, `stats`, `info`, `version`.
  - Discovery: VOSI server capability discovery (`canfar/utils/vosi.py`, `utils/discover.py`).
  - UX: Consistent output formatting and better help/usage text; sorting by start time; improved “stats” reporting usage instead of requested resources.

- Authentication and HTTP hooks (package `canfar/auth` and `canfar/hooks/httpx`):
  - OIDC token refresh hooks (sync and async) integrated with HTTPX.
  - X.509 improvements: default expiry set to 30 days, better error handling, lazy certificate path checks.
  - Custom exception types for clearer error semantics.

- Client API (package `canfar/client.py`):
  - Higher-level Python client with improved error handling (e.g., `AuthExpiryError`).
  - Richer typed models for `config`, `auth`, `session`, `registry`, and `http`.

- Utilities (package `canfar/utils`):
  - Display/logging helpers for nicer CLI output.
  - Discovery, build, convert, JWT expiry, VOSI helpers.
  - Name/garble helpers and other quality-of-life utilities.

## Improvements and Fixes

- Registry and environments:
  - Dev registries discoverable only with `--dev`.
  - Added/upgraded default config to support “auth tracking” from upstream servers.

- Stability and correctness:
  - Numerous CLI fixes: alias behavior, desktop-app info, stats output, request size, and sorting.
  - Model updates: new statuses like `Failed` and `Terminating`, updated kinds including `desktop-app`.

- Tooling and CI/CD:
  - Project migrated to `opencadc/canfar`; updated actions and release workflows.
  - Pre-commit refreshed; ruff centralizes linting/formatting/import sorting.
  - Expanded pytest configuration (coverage thresholds, xdist parallelization, markers).
  - mypy strictness increased with pydantic plugin.

## Documentation

- New structure under `docs/`:
  - Client docs: getting started, quick start, advanced examples, testing, migration, updates.
  - Platform docs: accounts, concepts, containers, batch jobs, guides with extensive screenshots.
  - About: governance, partners, terms, acknowledgements, and more.
- CLI documentation:
  - Authentication contexts, comprehensive CLI help, quick starts.
- Legacy docs reorganized and linked; numerous formatting and navigation fixes.
- Site URLs updated to `https://opencadc.github.io/canfar/`.

## Dependency and Packaging Changes

- `pyproject.toml`:
  - Name: `canfar`; Version: `0.8.0` (pre-1.0 HEAD).
  - Requires Python `>= 3.10`.
  - Dependencies include: `cadcutils`, `httpx[http2]`, `humanize`, `pydantic-settings`, `pyyaml`, `questionary`, `segno`.
  - Tooling config: ruff (lint/format), mypy, pytest, coverage, bandit, vulture, interrogate.
  - Scripts: `canfar = "canfar.cli.main:main"`.
  - URLs point to `opencadc/canfar`.

## Migration Guide (v0.7 → v1.0)

- Imports:
  - Before: `from skaha.client import SkahaClient`
  - After: `from canfar.client import Client` (or the appropriate equivalent)
- CLI:
  - Before: `skaha ...` or `cf ...`
  - After: `canfar ...`
  - Use `canfar --help` to discover updated subcommands; avoid removed aliases like `list`/`rm`.
- Auth:
  - OIDC refresh occurs via HTTPX hooks; ensure token context is properly configured.
  - X.509 defaults changed; certificate paths are lazily resolved.
- Config/models:
  - Expect a flattened config; update any code relying on nested auth models.
- Python/runtime:
  - Upgrade to Python 3.10+.
- Dependencies:
  - If you relied on `pyjwt` specifics, switch to `canfar/utils/jwt.py` helpers for expiry checks.

## Notable Diffs and Structural Changes

- Removed `skaha/` modules (package fully replaced).
- New modules under `canfar/`:
  - `auth/` (OIDC, X.509), `cli/` (commands), `hooks/httpx/` (auth/errors/expiry),
    `models/` (auth, config, http, registry, session, types),
    `utils/` (build, convert, discover, display, funny, garble, jwt, logging, vosi),
    `exceptions/` (typed exceptions).
- Docs greatly expanded with new sections and images, including platform guides.

## What’s Next

- Validate your environment on Python 3.10+ and the new `canfar` CLI.
- Update imports and scripts to use `canfar` packages and entrypoint.
- Review the new auth flows (OIDC refresh, X.509 behavior) and adjust configuration.
- Consult the new docs site for CLI usage and platform operations.

If you’d like, I can extract a targeted changelog for a specific area (e.g., only CLI, only auth) or produce a PR-ready `MIGRATION.md` based on the above.
