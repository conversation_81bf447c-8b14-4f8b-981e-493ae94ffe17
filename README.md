# CANFAR Python Client

[![Continous Integration](https://github.com/opencadc/canfar/actions/workflows/ci.yml/badge.svg)](https://github.com/opencadc/canfar/actions/workflows/ci.yml)
[![Continous Deployment](https://github.com/opencadc/canfar/actions/workflows/cd.yml/badge.svg)](https://github.com/opencadc/canfar/actions/workflows/cd.yml)
[![codecov](https://codecov.io/gh/opencadc/canfar/graph/badge.svg)](https://codecov.io/gh/opencadc/canfar)
[![CodeQL](https://github.com/opencadc/canfar/actions/workflows/codeql-analysis.yml/badge.svg)](https://github.com/opencadc/canfar/actions/workflows/codeql-analysis.yml)
[![OpenSSF Scorecard](https://api.scorecard.dev/projects/github.com/opencadc/canfar/badge)](https://scorecard.dev/viewer/?uri=github.com/opencadc/canfar)

## For more information, [check out the documentation](https://opencadc.github.io/canfar/).

---
<p align="center">
  <a href="Some Love">
    <img src="https://forthebadge.com/images/badges/built-with-love.svg">
  </a>
</p>
