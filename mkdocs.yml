site_name: CANFAR
site_url: https://opencadc.github.io/canfar/
site_author: <PERSON><PERSON>
site_description: CANFAR Science Platform
repo_url: https://github.com/opencadc/canfar/
repo_name: opencadc/canfar
edit_uri: blob/main/docs/
copyright: Copyright &copy; 1986-2025 Canadian Astronomy Data Centre
remote_branch: gh-pages

theme:
  name: material
  palette:
    - media: "(prefers-color-scheme)"
      toggle:
        icon: material/link
        name: Switch to light mode
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: indigo
      accent: indigo
      toggle:
        icon: material/toggle-switch
        name: Switch to dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: black
      accent: indigo
      toggle:
        icon: material/toggle-switch-off
        name: Switch to system preference
  font:
    text: Roboto
    code: Roboto Mono
  language: en
  icon:
    repo: fontawesome/brands/github-alt
    edit: material/pencil-box
    view: material/eye-outline
  features:
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.instant
    - navigation.instant.progress
    - navigation.prune
    - navigation.sections
    - navigation.tracking
    - navigation.indexes
    - navigation.expand
    - navigation.instant.prefetch
    - toc.follow
    # - toc.integrate
    - navigation.top
    - announce.dismiss
    - content.code.annotate
    - content.action.edit
    - content.action.view
    - content.code.copy
    - content.code.select
    - content.footnote.tooltips
    - content.tabs.link
    - content.tooltips
    - search.highlight
    - search.share
    - search.suggest

plugins:
  - search:
      separator: '[\s\u200b\-_,:!=\[\]()"`/]+|\.(?!\d)|&[lg]t;|(?!\b)(?=[A-Z][a-z])'
  - mkdocstrings:
      handlers:
      python:
        options:
          show_symbol_type_toc: true
          show_signature: true
          separate_signature: true
          line_length: 88
          annotations_path: brief
          docstring_style: google
          docstring_section_style: spacy
          docstring_options:
            ignore_init_summary: true
            trim_doctest_flags: true
  - git-revision-date-localized:
        type: date
        enable_creation_date: true
        fallback_to_build_date: true
  - git-committers:
      enabled: true
      repository: opencadc/canfar
      branch: main
  - termynal

extra:
  version:
    provider: mike
    default: latest
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/opencadc/canfar
    - icon: fontawesome/brands/python
      link: https://pypi.org/project/canfar
    - icon: fontawesome/brands/docker
      link: images.canfar.net//harbor/projects
    - icon: fontawesome/brands/discord
      link: https://discord.gg/vcCQ8QBvBa
    - icon: fontawesome/solid/paper-plane
      link: mailto:<EMAIL>
  analytics:
    feedback:
      title: Was this page helpful?
      ratings:
        - icon: material/emoticon-happy-outline
          name: This page was helpful
          data: 1
          note: Thanks for your feedback!
        - icon: material/emoticon-sad-outline
          name: This page could be improved
          data: 0
          note: Let us know how we can improve this page.

# Extensions
markdown_extensions:
  - markdown.extensions.admonition
  - markdown.extensions.attr_list
  - markdown.extensions.md_in_html
  - markdown.extensions.def_list
  - markdown.extensions.footnotes
  - markdown.extensions.meta
  - markdown.extensions.toc:
      permalink: true
      toc_depth: 2
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.critic
  - pymdownx.details
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.highlight:
      use_pygments: true
      linenums_style: pymdownx.inline
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      repo_url_shorthand: true
      user: squidfunk
      repo: mkdocs-material
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.snippets:
      check_paths: true
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tilde

nav:
  - Home: index.md
  - Platform Concepts:
    - Home: platform/home.md
    - Getting Started: platform/get-started.md
    - User Guides:
      - Intro: platform/guides/index.md
      - Concepts: platform/concepts.md
      - Accounts: platform/accounts.md
      - Storage: platform/guides/storage/index.md
      - VOSpace API: platform/guides/storage/vospace-api.md
      - Containers: platform/containers.md
      - Interactive Sessions:
        - Home: platform/guides/interactive-sessions/index.md
        - Jupyter Notebooks: platform/guides/interactive-sessions/launch-notebook.md
        - Desktop Sessions: platform/guides/interactive-sessions/launch-desktop.md
        - CARTA Visualization: platform/guides/interactive-sessions/launch-carta.md
        - Firefly Table Viewer: platform/guides/interactive-sessions/launch-firefly.md
        - Contributed Applications: platform/guides/interactive-sessions/launch-contributed.md
      - Batch Jobs: platform/batch-jobs.md
      - Radio Astronomy:
        - Home: platform/guides/radio-astronomy/index.md
        - CASA Workflows: platform/guides/radio-astronomy/casa-workflows.md
      - Publications: platform/legacy/publication.md
      - Legacy:
        - Cloud Services: platform/legacy/cloud-services.md
        - VOS Storage: platform/legacy/storage.md
    - Help & Support: platform/help.md
  - Client:
    - Home: client/home.md
    - Authentication: cli/authentication-contexts.md
    - What's New:
      - Updates: client/updates.md
      - Migration Guide: client/migration.md
    - Get Started:
      - Installation & Setup: client/get-started.md
      - 5-Minute Tutorial: client/quick-start.md
    - Examples:
      - Basic: client/examples.md
      - Advanced: client/advanced-examples.md
    - Reference:
      - Python API:
        - AsyncSession: client/async_session.md
        - Session: client/session.md
        - Images: client/images.md
        - Context: client/context.md
        - Overview: client/overview.md
        - HTTPClient: client/client.md
        - Helpers: client/helpers.md
    - CLI:
      - 5-Minute Tutorial: cli/quick-start.md
      - Reference: cli/cli-help.md
  - FAQ: faq.md
  - Contributor Guide:
    - Contributing: contributing.md
    - Code of Conduct: conduct.md
    - OpenSSF Report: https://scorecard.dev/viewer/?uri=github.com/opencadc/canfar
    - License: license.md
    - Bug Reports:
      - Bugs: bug-reports.md
      - Security Issues: security.md
    - Changelog: changelog.md
  - About:
    - Home: about/home.md
    - Governance: about/governance.md
    - Terms of Reference: about/terms.md
    - Partners: about/partners.md
    - Acknowledgement: about/acknowledgement.md
